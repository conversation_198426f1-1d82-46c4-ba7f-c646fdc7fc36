"use client"

import { Card } from "src/components/Card"
import { Page } from "src/components/Page"

import { useEffect, useState } from "react"
import { getSupabaseClient } from "src/lib/supabase"
import { <PERSON>ton, Text, Loader, Group, SimpleGrid, Accordion, Stack, ActionIcon, Tooltip, Code } from "@mantine/core"
import { IconCoin, IconBrandTwitter, IconLink, IconBrandTelegram, IconBrandYoutube, IconBrandInstagram, IconBrandFacebook, IconExternalLink, IconCopy, IconCheck, IconCurrencyBitcoin, IconCurrencyEthereum, IconCurrencySolana } from "@tabler/icons-react"
import { notifications } from '@mantine/notifications'
import { useParams } from "next/navigation"
import { ContactCard } from "src/components/Card/contact"
import { useFooter } from "src/providers/FooterProvider"
import { AddToMobileContactButton } from "src/components/Buttons/AddToMobileContactButton"

interface ContactItem {
  name: string;
  description: string | null;
  image: string | null;
  uri: string | null;
  profile: string | null;
  email: string | null;
  website: string | null;
  phone: string | null;
  tg_bot: string | null;
  notes: string | null;
  eth: string | null;
  bsc: string | null;
  matic: string | null;
  btc: string | null;
  fil: string | null;
  sol: string | null;
  twitter: string | null;
  telegram: string | null;
  youtube: string | null;
  instagram: string | null;
  facebook: string | null;
  web2: string | null;
  web3: string | null;
  links: Record<string, string> | null;
  images: Record<string, string> | null;
  minted: string | null;
}

export default function ProfilePage() {
  const [contact, setContact] = useState<ContactItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);
  const params = useParams();
  const { setFooterContent, clearFooterContent } = useFooter();
  // Handle both encoded and unencoded URLs, convert to lowercase
  const rawId = params?.id as string;
  const contactName = rawId && rawId.includes('%') ? decodeURIComponent(rawId).toLowerCase() : rawId?.toLowerCase();

  useEffect(() => {
    if (!contactName) {
      setError('Invalid contact name');
      setLoading(false);
      return;
    }

    const fetchContactData = async () => {
      try {
        console.log('Fetching contact:', contactName);
        console.log('Raw params.id:', params?.id);
        console.log('Decoded contactName:', contactName);

        const client = getSupabaseClient();
        const { data, error } = await client
          .from('contact')
          .select('*')
          .eq('name', contactName)
          .single();

        console.log('Contact query results:', { data, error });

        if (error) {
          console.error('Supabase error:', error);
          throw error;
        }

        if (data) {
          //console.log('Contact found:', data);
          // Handle links and images as JSON objects
          const processedData = {
            ...data,
            links: data.links ? (typeof data.links === 'string' ? JSON.parse(data.links) : data.links) : null,
            images: data.images ? (typeof data.images === 'string' ? JSON.parse(data.images) : data.images) : null
          };
          setContact(processedData);
        } else {
          console.log('No contact data returned');
          setError(`Contact not found: ${contactName}. Check if name matches exactly.`);
        }
      } catch (error) {
        console.error('Error fetching contact data:', error);
        setError(`Failed to fetch contact data: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    };

    fetchContactData();
  }, [contactName]);

  // Set footer content when contact is loaded
  useEffect(() => {
    if (contact) {
      setFooterContent(<AddToMobileContactButton contact={contact} />);
    } else {
      clearFooterContent();
    }
  }, [contact]); // Only depend on contact

  // Clear footer content when component unmounts
  useEffect(() => {
    return () => {
      clearFooterContent();
    };
  }, []); // Empty dependency array for cleanup only

  // Helper function to copy wallet address
  const copyToClipboard = async (address: string, currency: string) => {
    try {
      await navigator.clipboard.writeText(address);
      setCopiedAddress(address);
      notifications.show({
        title: 'Copied!',
        message: `${currency} address copied to clipboard`,
        color: 'green',
      });
      setTimeout(() => {
        setCopiedAddress(null);
      }, 2000);
    } catch (err) {
      notifications.show({
        title: 'Error',
        message: 'Failed to copy address',
        color: 'red',
      });
    }
  };

  // Helper function to get crypto icon
  const getCryptoIcon = (currency: string) => {
    switch (currency.toLowerCase()) {
      case 'btc':
        return <IconCurrencyBitcoin size={16} />;
      case 'eth':
        return <IconCurrencyEthereum size={16} />;
      case 'sol':
        return <IconCurrencySolana size={16} />;
      default:
        return <IconCoin size={16} />;
    }
  };

  // Helper function to get social media URL
  const getSocialUrl = (platform: string, handle: string): string => {
    const cleanHandle = handle.replace(/^@/, '');
    switch (platform.toLowerCase()) {
      case 'twitter':
        return `https://twitter.com/${cleanHandle}`;
      case 'telegram':
        return `https://t.me/${cleanHandle}`;
      case 'instagram':
        return `https://instagram.com/${cleanHandle}`;
      case 'facebook':
        return handle.startsWith('http') ? handle : `https://facebook.com/${cleanHandle}`;
      case 'youtube':
        return handle.startsWith('http') ? handle : `https://youtube.com/@${cleanHandle}`;
      default:
        return handle.startsWith('http') ? handle : `https://${handle}`;
    }
  };

  // Helper functions to check if sections have any values
  const hasCryptoValues = (contact: ContactItem): boolean => {
    return !!(
      (contact.eth && contact.eth.trim()) ||
      (contact.bsc && contact.bsc.trim()) ||
      (contact.matic && contact.matic.trim()) ||
      (contact.btc && contact.btc.trim()) ||
      (contact.fil && contact.fil.trim()) ||
      (contact.sol && contact.sol.trim())
    );
  };

  const hasSocialValues = (contact: ContactItem): boolean => {
    return !!(
      (contact.twitter && contact.twitter.trim()) ||
      (contact.telegram && contact.telegram.trim()) ||
      (contact.youtube && contact.youtube.trim()) ||
      (contact.instagram && contact.instagram.trim()) ||
      (contact.facebook && contact.facebook.trim())
    );
  };

  const hasLinksValues = (contact: ContactItem): boolean => {
    const hasCustomLinks = contact.links && Object.entries(contact.links).some(([key, value]) =>
      key.trim() && value.trim()
    );
    return !!(
      hasCustomLinks ||
      (contact.web2 && contact.web2.trim()) ||
      (contact.web3 && contact.web3.trim())
    );
  };

  // Determine the default accordion value based on what sections have content
  const getDefaultAccordionValue = (contact: ContactItem): string => {
    if (hasSocialValues(contact)) return "social";
    if (hasCryptoValues(contact)) return "crypto";
    if (hasLinksValues(contact)) return "links";
    return "social"; // fallback to social
  };



  return (
    <Page>
      <Card>
        {error && (
          <Text c="red" ta="center" mb="md">
            {error}
          </Text>
        )}

        {loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', padding: '1rem' }}>
            <Loader />
          </div>
        ) : contact ? (
          <Stack gap="md">
            <SimpleGrid
              cols={{ base: 1, md: 2, lg: 2 }}
              spacing={{ base: 10, sm: 'xs' }}
              verticalSpacing={{ base: 'md', sm: 'xl' }}
            >
              <ContactCard name={contact.name} />
          {/* Show accordion only if there's at least one section with content */}
          {(hasCryptoValues(contact) || hasSocialValues(contact) || hasLinksValues(contact)) ? (
            <Accordion defaultValue={getDefaultAccordionValue(contact)} variant="separated">
  {/* Social Media Section - Only show if there are social values */}
            {hasSocialValues(contact) && (
              <Accordion.Item value="social">
                <Accordion.Control icon={<IconBrandTwitter size={20} />}>
                  Social Media
                </Accordion.Control>
                <Accordion.Panel>
                  <Group justify="center" gap="lg">
                    {contact.twitter && contact.twitter.trim() && (
                      <Tooltip label={`Twitter: ${contact.twitter}`} withArrow>
                        <ActionIcon
                          component="a"
                          href={getSocialUrl('twitter', contact.twitter)}
                          target="_blank"
                          rel="noopener noreferrer"
                          size={60}
                          radius="xl"
                          style={{
                            backgroundColor: '#1DA1F2',
                            color: 'white',
                            '&:hover': { backgroundColor: '#0d8bd9' }
                          }}
                        >
                          <IconBrandTwitter size={30} />
                        </ActionIcon>
                      </Tooltip>
                    )}

                    {contact.instagram && contact.instagram.trim() && (
                      <Tooltip label={`Instagram: ${contact.instagram}`} withArrow>
                        <ActionIcon
                          component="a"
                          href={getSocialUrl('instagram', contact.instagram)}
                          target="_blank"
                          rel="noopener noreferrer"
                          size={60}
                          radius="xl"
                          style={{
                            background: 'linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%)',
                            color: 'white'
                          }}
                        >
                          <IconBrandInstagram size={30} />
                        </ActionIcon>
                      </Tooltip>
                    )}

                    {contact.facebook && contact.facebook.trim() && (
                      <Tooltip label={`Facebook: ${contact.facebook}`} withArrow>
                        <ActionIcon
                          component="a"
                          href={getSocialUrl('facebook', contact.facebook)}
                          target="_blank"
                          rel="noopener noreferrer"
                          size={60}
                          radius="xl"
                          style={{
                            backgroundColor: '#1877F2',
                            color: 'white',
                            '&:hover': { backgroundColor: '#166fe5' }
                          }}
                        >
                          <IconBrandFacebook size={30} />
                        </ActionIcon>
                      </Tooltip>
                    )}

                    {contact.youtube && contact.youtube.trim() && (
                      <Tooltip label={`YouTube: ${contact.youtube}`} withArrow>
                        <ActionIcon
                          component="a"
                          href={getSocialUrl('youtube', contact.youtube)}
                          target="_blank"
                          rel="noopener noreferrer"
                          size={60}
                          radius="xl"
                          style={{
                            backgroundColor: '#FF0000',
                            color: 'white',
                            '&:hover': { backgroundColor: '#cc0000' }
                          }}
                        >
                          <IconBrandYoutube size={30} />
                        </ActionIcon>
                      </Tooltip>
                    )}

                    {contact.telegram && contact.telegram.trim() && (
                      <Tooltip label={`Telegram: ${contact.telegram}`} withArrow>
                        <ActionIcon
                          component="a"
                          href={getSocialUrl('telegram', contact.telegram)}
                          target="_blank"
                          rel="noopener noreferrer"
                          size={60}
                          radius="xl"
                          style={{
                            backgroundColor: '#0088cc',
                            color: 'white',
                            '&:hover': { backgroundColor: '#006699' }
                          }}
                        >
                          <IconBrandTelegram size={30} />
                        </ActionIcon>
                      </Tooltip>
                    )}
                  </Group>
                </Accordion.Panel>
              </Accordion.Item>
            )}

            {/* Links Section - Only show if there are link values */}
            {hasLinksValues(contact) && (
              <Accordion.Item value="links">
                <Accordion.Control icon={<IconLink size={20} />}>
                  Links
                </Accordion.Control>
                <Accordion.Panel>
                  <Stack gap="sm">
                    {contact.links && Object.entries(contact.links)
                      .filter(([name, url]) => name.trim() && url.trim())
                      .map(([name, url]) => (
                        <Group key={name} justify="space-between">
                               <Button
                            leftSection={<IconExternalLink size={14} />}
                            variant="default"
                            size="xs"
                            component="a"
                            href={url.startsWith('http') ? url : `https://${url}`}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {name}
                          </Button>
                        </Group>
                      ))}

                  </Stack>
                </Accordion.Panel>
              </Accordion.Item>
            )}

            {/* Crypto Section - Only show if there are crypto values */}
            {hasCryptoValues(contact) && (
              <Accordion.Item value="crypto">
                <Accordion.Control icon={<IconCoin size={20} />}>
                  Cryptocurrency
                </Accordion.Control>
                <Accordion.Panel>
                  <Stack gap="sm">
                    {contact.eth && contact.eth.trim() && (
                      <Group justify="space-between" wrap="nowrap">
                        <Group gap="xs">
                          {getCryptoIcon('eth')}
                          <Text fw={500}>ETH:</Text>
                        </Group>
                        <Group gap="xs" style={{ flex: 1, minWidth: 0 }}>
                          <Code style={{
                            flex: 1,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            border: '1px solid var(--mantine-color-gray-3)',
                            padding: '4px 8px'
                          }}>
                            {contact.eth}
                          </Code>
                          <Tooltip label={copiedAddress === contact.eth ? "Copied!" : "Copy address"}>
                            <ActionIcon
                              variant="light"
                              size="sm"
                              onClick={() => copyToClipboard(contact.eth!, 'ETH')}
                              color={copiedAddress === contact.eth ? "green" : "blue"}
                            >
                              {copiedAddress === contact.eth ? <IconCheck size={14} /> : <IconCopy size={14} />}
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Group>
                    )}
                    {contact.bsc && contact.bsc.trim() && (
                      <Group justify="space-between" wrap="nowrap">
                        <Group gap="xs">
                          {getCryptoIcon('bsc')}
                          <Text fw={500}>BSC:</Text>
                        </Group>
                        <Group gap="xs" style={{ flex: 1, minWidth: 0 }}>
                          <Code style={{
                            flex: 1,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            border: '1px solid var(--mantine-color-gray-3)',
                            padding: '4px 8px'
                          }}>
                            {contact.bsc}
                          </Code>
                          <Tooltip label={copiedAddress === contact.bsc ? "Copied!" : "Copy address"}>
                            <ActionIcon
                              variant="light"
                              size="sm"
                              onClick={() => copyToClipboard(contact.bsc!, 'BSC')}
                              color={copiedAddress === contact.bsc ? "green" : "blue"}
                            >
                              {copiedAddress === contact.bsc ? <IconCheck size={14} /> : <IconCopy size={14} />}
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Group>
                    )}
                    {contact.matic && contact.matic.trim() && (
                      <Group justify="space-between" wrap="nowrap">
                        <Group gap="xs">
                          {getCryptoIcon('matic')}
                          <Text fw={500}>MATIC:</Text>
                        </Group>
                        <Group gap="xs" style={{ flex: 1, minWidth: 0 }}>
                          <Code style={{
                            flex: 1,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            border: '1px solid var(--mantine-color-gray-3)',
                            padding: '4px 8px'
                          }}>
                            {contact.matic}
                          </Code>
                          <Tooltip label={copiedAddress === contact.matic ? "Copied!" : "Copy address"}>
                            <ActionIcon
                              variant="light"
                              size="sm"
                              onClick={() => copyToClipboard(contact.matic!, 'MATIC')}
                              color={copiedAddress === contact.matic ? "green" : "blue"}
                            >
                              {copiedAddress === contact.matic ? <IconCheck size={14} /> : <IconCopy size={14} />}
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Group>
                    )}
                    {contact.btc && contact.btc.trim() && (
                      <Group justify="space-between" wrap="nowrap">
                        <Group gap="xs">
                          {getCryptoIcon('btc')}
                          <Text fw={500}>BTC:</Text>
                        </Group>
                        <Group gap="xs" style={{ flex: 1, minWidth: 0 }}>
                          <Code style={{
                            flex: 1,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            border: '1px solid var(--mantine-color-gray-3)',
                            padding: '4px 8px'
                          }}>
                            {contact.btc}
                          </Code>
                          <Tooltip label={copiedAddress === contact.btc ? "Copied!" : "Copy address"}>
                            <ActionIcon
                              variant="light"
                              size="sm"
                              onClick={() => copyToClipboard(contact.btc!, 'BTC')}
                              color={copiedAddress === contact.btc ? "green" : "blue"}
                            >
                              {copiedAddress === contact.btc ? <IconCheck size={14} /> : <IconCopy size={14} />}
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Group>
                    )}
                    {contact.fil && contact.fil.trim() && (
                      <Group justify="space-between" wrap="nowrap">
                        <Group gap="xs">
                          {getCryptoIcon('fil')}
                          <Text fw={500}>FIL:</Text>
                        </Group>
                        <Group gap="xs" style={{ flex: 1, minWidth: 0 }}>
                          <Code style={{
                            flex: 1,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            border: '1px solid var(--mantine-color-gray-3)',
                            padding: '4px 8px'
                          }}>
                            {contact.fil}
                          </Code>
                          <Tooltip label={copiedAddress === contact.fil ? "Copied!" : "Copy address"}>
                            <ActionIcon
                              variant="light"
                              size="sm"
                              onClick={() => copyToClipboard(contact.fil!, 'FIL')}
                              color={copiedAddress === contact.fil ? "green" : "blue"}
                            >
                              {copiedAddress === contact.fil ? <IconCheck size={14} /> : <IconCopy size={14} />}
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Group>
                    )}
                    {contact.sol && contact.sol.trim() && (
                      <Group justify="space-between" wrap="nowrap">
                        <Group gap="xs">
                          {getCryptoIcon('sol')}
                          <Text fw={500}>SOL:</Text>
                        </Group>
                        <Group gap="xs" style={{ flex: 1, minWidth: 0 }}>
                          <Code style={{
                            flex: 1,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            border: '1px solid var(--mantine-color-gray-3)',
                            padding: '4px 8px'
                          }}>
                            {contact.sol}
                          </Code>
                          <Tooltip label={copiedAddress === contact.sol ? "Copied!" : "Copy address"}>
                            <ActionIcon
                              variant="light"
                              size="sm"
                              onClick={() => copyToClipboard(contact.sol!, 'SOL')}
                              color={copiedAddress === contact.sol ? "green" : "blue"}
                            >
                              {copiedAddress === contact.sol ? <IconCheck size={14} /> : <IconCopy size={14} />}
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Group>
                    )}
                  </Stack>
                </Accordion.Panel>
              </Accordion.Item>
            )}
          </Accordion>
          ) : (
            <Text ta="center" c="dimmed" mt="xl">
              No additional information available
            </Text>
          )}
            </SimpleGrid>
          </Stack>
        ) : (
          <div style={{ textAlign: 'center' }}>
            <Text c="dimmed" mb="md">
              Contact not found: {contactName}
            </Text>
            <Text size="sm" c="dimmed" mb="md">
              This contact doesn't exist in the database yet.
            </Text>

          </div>
        )}
      </Card>
    </Page>
  );
}
