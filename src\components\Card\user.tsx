'use client';
import { IconChevronRight } from '@tabler/icons-react';
import { Avatar, Group, Text, UnstyledButton } from '@mantine/core';
import classes from './user.module.css';
import { useState, useEffect } from 'react';
import { fetchData } from 'src/lib/supabase';
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { getFirstImage, ImageData } from 'src/lib/common';

interface UserInfo {
  profile: string;
  email: string;
  phone?: string;
  image?: string;
  profile_email?: string;
  images?: ImageData;
  description?: string;
  twitter?: string;
  telegram?: string;
  youtube?: string;
  instagram?: string;
  facebook?: string;
  web2?: string;
  web3?: string;
}

export function UserInfo({ name }: { name: string }) {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const { data: session } = useSession();
  const router = useRouter();



  const fetchUserData = async () => {
    try {
      setLoading(true);
      const { data } = await fetchData<UserInfo>('contact', {
        select: 'profile, email, image, profile_email, images, description, twitter, telegram, youtube, instagram, facebook, web2, web3',
        filter: [{ column: 'name', value: name.toLowerCase() }],
        single: true
      });
      //console.log('User data from Database:', data);
      const userData = Array.isArray(data) ? data[0] : data;
      setUserInfo(userData);

      // Check if signed-in user's email matches profile_email
      //console.log('Session email:', session?.user?.email);
     // console.log('Profile email:', (userData as any)?.profile_email);
    } catch (error) {
      console.error('Error fetching user data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserData();
  }, [name]);

  if (loading) return <div>Loading...</div>;
  if (!userInfo) return <div>User not found : {name}</div>;

  const handleClick = () => {
    // Check if signed-in user's email matches profile_email
    const sessionEmail = session?.user?.email;
    if (sessionEmail && userInfo?.profile_email === sessionEmail) {
      // User owns this contact, redirect to tools page
      router.push(`/tools/${name.toLowerCase()}`);
    } else {
      // User doesn't own this contact, redirect to profile page
      router.push(`/profile/${name.toLowerCase()}`);
    }
  };

  return (
    <UnstyledButton className={classes.user} onClick={handleClick}>
      <Group>
        <Avatar
          src={getFirstImage(userInfo.images)}
          radius="xl"
        />

        <div style={{ flex: 1 }}>
          <Text size="sm" fw={500}>
            {userInfo.profile}
          </Text>

          <Text c="dimmed" size="xs">
            {name}
          </Text>
        </div>

        <IconChevronRight size={14} stroke={1.5} />
      </Group>
    </UnstyledButton>
  );
}
