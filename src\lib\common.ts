import { DEFAULT_AVATAR_URL, MAX_ODUDE_NAME_LENGTH, MAX_ODUDE_NAME_SEGMENT_LENGTH, DOMAIN_API_BASE_URL } from './config';

export const getUserValue = (email: string | null | undefined, column: string) => {
  if (typeof localStorage !== "undefined") {
    return localStorage.getItem(`${column}_${email}`);
  }
  return null;
};

export const setUserValue = (email: string | null | undefined, column: string, value: string) => {
  console.log("set");
  if (typeof localStorage !== "undefined") {
    localStorage.setItem(`${column}_${email}`, value);
    console.log("set", email, column, value);
  }
};

export const deleteUserValue = (email: string | null | undefined, column: string) => {
  if (typeof localStorage !== "undefined") {
    localStorage.removeItem(`${column}_${email}`);
  }
};

export function isValidODudeName(odudeName: string): boolean {
  const atCount = odudeName.split('@').length - 1;
  if (atCount > 1) return false;

  return (
    /^[a-z\d]([a-z\d.]*[a-z\d])?(@[a-z\d]([a-z\d.]*[a-z\d])?)*$/i.test(odudeName) &&
    new RegExp(`^.{1,${MAX_ODUDE_NAME_LENGTH}}$`).test(odudeName) &&
    new RegExp(`^[^@]{1,${MAX_ODUDE_NAME_SEGMENT_LENGTH}}(@[^@]{1,${MAX_ODUDE_NAME_SEGMENT_LENGTH}})*$`).test(odudeName)
  );
}

/**
 * Fetches domain information from a remote API.
 *
 * @param name - The domain name to fetch information for.
 * @returns A promise that resolves to a JSON object containing the domain information.
 */
export async function fetchDomain(name: string): Promise<any> {
  const randomNumber = Math.random();
  const url = `${DOMAIN_API_BASE_URL}?name=${name}&${randomNumber}`;
  try {
      const response = await fetch(url);
      if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
  } catch (error) {
      return { error: (error as Error).message };
  }
}

/**
 * Fetches NFT owner information from a remote API via internal proxy.
 *
 * @param name - The name to fetch NFT owner information for.
 * @returns A promise that resolves to the owner address if found, or null if not found or error.
 */
export async function fetchNFTowner(name: string): Promise<string | null> {
  const url = `/api/nft-owner?name=${encodeURIComponent(name)}`;
  try {
      const response = await fetch(url);
      if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      // Return the address if it exists and is not null or blank
      if (data.address && data.address.trim() !== '') {
          return data.address;
      }
      return null;
  } catch (error) {
      console.error('Error fetching NFT owner:', (error as Error).message);
      return null;
  }
}



// Interface for images structure
export interface ImageData {
  img1?: string;
  img2?: string;
  img3?: string;
}

/**
 * Gets the first available image from the images object
 * @param images - The images object containing img1, img2, img3 properties
 * @returns The first available image URL or default avatar URL
 */
export const getFirstImage = (images?: ImageData | string | null): string => {
  if (!images) {
    return DEFAULT_AVATAR_URL;
  }

  // Handle both string JSON and object formats
  let imageData: ImageData;
  try {
    imageData = typeof images === 'string' ? JSON.parse(images) : images;
  } catch (error) {
    console.error('Error parsing images data:', error);
    return DEFAULT_AVATAR_URL;
  }

  // Return first available image in order: img1, img2, img3
  return imageData.img1 || imageData.img2 || imageData.img3 || DEFAULT_AVATAR_URL;
};

/**
 * Gets a specific image from the images object based on index
 * @param images - The images object containing img1, img2, img3 properties
 * @param index - The image index (1, 2, or 3)
 * @returns The specified image URL or default avatar URL
 */
export const getImage = (images?: ImageData | string | null, index: number = 1): string => {
  if (!images) {
    return DEFAULT_AVATAR_URL;
  }

  // Handle both string JSON and object formats
  let imageData: ImageData;
  try {
    imageData = typeof images === 'string' ? JSON.parse(images) : images;
  } catch (error) {
    console.error('Error parsing images data:', error);
    return DEFAULT_AVATAR_URL;
  }

  // Get the specific image based on index
  const imageKey = `img${index}` as keyof ImageData;
  const specificImage = imageData[imageKey];

  if (specificImage) {
    return specificImage;
  }

  // If specific image not found, fall back to first available image
  return imageData.img1 || imageData.img2 || imageData.img3 || DEFAULT_AVATAR_URL;
};

/**
 * Detects if the current device is an iOS device (iPhone, iPad, iPod)
 * @returns true if the device is iOS, false otherwise
 */
export const isIOSDevice = (): boolean => {
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false;
  }

  const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;

  // Check for iOS devices
  return /iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream;
};

/**
 * Resets iOS Safari zoom by temporarily setting viewport meta tag
 * This fixes the issue where zoom persists after input field interaction
 */
export const resetIOSZoom = (): void => {
  if (!isIOSDevice() || typeof document === 'undefined') {
    return;
  }

  // Find existing viewport meta tag
  let viewportMeta = document.querySelector('meta[name="viewport"]') as HTMLMetaElement;

  if (!viewportMeta) {
    // Create viewport meta tag if it doesn't exist
    viewportMeta = document.createElement('meta');
    viewportMeta.name = 'viewport';
    document.head.appendChild(viewportMeta);
  }

  // Store original content
  const originalContent = viewportMeta.content;

  // Temporarily disable zoom
  viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';

  // Restore original viewport settings after a short delay
  setTimeout(() => {
    if (viewportMeta) {
      viewportMeta.content = originalContent || 'width=device-width, initial-scale=1.0';
    }
  }, 100);
};

