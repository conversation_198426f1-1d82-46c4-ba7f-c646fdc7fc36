"use client"

import { useSession } from "next-auth/react"
import { UserInfo } from "../Card/user";
import {ActionsGrid } from "../Card/ActionsGrid";

import { Divider, Grid } from '@mantine/core';
import { fetchData, insertData } from '../../lib/supabase';
import { useEffect, useState } from "react";
import { getDataValue } from '../../lib/supabase';
import { getUserValue, setUserValue } from '../../lib/common';
import { MAX_CONTACTS_LIMIT } from '../../lib/config';

export const SignedIn = () => {
  const { data: session } = useSession();
  const [contacts, setContacts] = useState<any[]>([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const fetchContacts = async () => {
    if (session?.user?.email) {
      const { data, error } = await fetchData('contact', {
        select: 'name, image, profile',
        filter: [{
          column: 'profile_email',
          value: session.user.email
        }]
      });

      if (!error && data) {
        setContacts(data as any[]);
      }
    }
  };

  const triggerRefresh = () => {
    fetchContacts();
    setRefreshTrigger(prev => prev + 1); // This will trigger ActionsGrid to refresh
  };

  useEffect(() => {
    fetchContacts();
  }, [session]);

  const saveUserData = async () => {
    if (session) {
      const { name: full_name, image: avatar_url } = session?.user || {};
     // console.log('User data from google:', full_name, avatar_url);
    //  console.log('Full name from LocalStorage:', getUserValue(session?.user?.email,'full_name'));



      const { email } = session?.user || {};

      if (getUserValue(email,'full_name') == null) {
        console.log('Set local storage');
        setUserValue(email, 'full_name', full_name || '');
        setUserValue(email, 'avatar_url', avatar_url || '');
       // console.log(avatar_url);
        window.dispatchEvent(new Event('userDataUpdated'));
        return;
      }

      const existingData = email ? await getDataValue('profiles', 'email', email, 'full_name') : null;
      if (existingData !== null) {
        console.log('Email already exists, skipping saveUserData.');
        return;
      }


      const { data, error } = await insertData('profiles', { full_name, email, avatar_url });
      if (!error) {
        console.log('User data saved successfully:', data);
      } else {
        console.error('Error saving user data:', error);
      }
    }
  };

  useEffect(() => {
    saveUserData();
  }, [session]);

  return (
   <div>
          <ActionsGrid onContactCreated={fetchContacts} refreshTrigger={refreshTrigger}/>
            <Divider id="div-label" my="xs" label={`My ODude Names (${contacts.length}/${MAX_CONTACTS_LIMIT})`} labelPosition="center" w="100%" />
          <Grid>

        {contacts.map((contact) => (
                 <Grid.Col key={contact.name} span={6}><UserInfo name={contact.name} /></Grid.Col>
        ))}
  </Grid>
      </div>


  )
}


